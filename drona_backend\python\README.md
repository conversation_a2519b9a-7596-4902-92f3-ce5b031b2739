# PDF Question and Solution Extraction Tool

A comprehensive Python tool that processes PDF documents using Google Gemini AI (primary) and Mistral AI (OCR/fallback) to extract both questions with multiple-choice options AND detailed solution steps from educational/test materials.

## Features

### Question Extraction
- **PDF Processing**: Uses Mistral AI's OCR API to extract text and images from PDF documents
- **Question Identification**: Google Gemini AI-powered extraction of multiple-choice questions and options
- **Answer Mapping**: Automatically maps answers from PDF answer key sections to questions
- **Structured Output**: Formats extracted content in JSON format with enhanced structure

### Solution Extraction (NEW)
- **Step-by-Step Solutions**: Extracts detailed solution procedures and methodologies
- **Educational Focus**: Captures HOW to solve problems, not just final answers
- **Methodology Identification**: Identifies solution approaches and key concepts used
- **Comprehensive Coverage**: Handles mathematical derivations, chemical reactions, and procedural steps

### General Features
- **Image Support**: Preserves and processes images referenced in questions and solutions
- **Batch Processing**: Can process single files or entire directories
- **Flexible Output**: JSON format with optional custom formatting
- **Error Handling**: Robust error handling and validation
- **CLI Interface**: Easy-to-use command-line interface

## Requirements

- **Python 3.7+**
- **Google Gemini API key** - Get yours at [Google AI Studio](https://makersuite.google.com/app/apikey)
- **Mistral AI API key** (optional, for OCR) - Get yours at [Mistral AI](https://mistral.ai/)
- **Required packages**: See `requirements.txt`

## Quick Installation

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Set up your API keys:
```bash
# Required: Google Gemini API key
export GEMINI_API_KEY=your-gemini-api-key-here

# Optional but recommended: Mistral AI API key for OCR
echo "your-mistral-api-key-here" > key.txt
```

For detailed installation instructions, see [INSTALLATION.md](INSTALLATION.md).
For Google Gemini AI setup, see [GEMINI_SETUP.md](GEMINI_SETUP.md).

## Usage

### Command Line Interface

#### Question Extraction (Original Functionality)
```bash
# Extract questions from a PDF
python pdf_question_parser.py document.pdf

# Extract questions with custom output file
python pdf_question_parser.py document.pdf -o questions.json

# Extract questions with JSON output
python pdf_question_parser.py document.pdf -j
```

#### Solution Extraction (NEW Feature)
```bash
# Extract detailed solutions from a PDF
python pdf_question_parser.py document.pdf -s

# Extract solutions with custom output file
python pdf_question_parser.py document.pdf -s -o solutions.json

# Extract solutions with JSON output
python pdf_question_parser.py document.pdf -s -j
```

#### Batch Processing
```bash
# Process all PDFs in a directory
python pdf_question_parser.py -d ./pdfs/ -od ./outputs/

# List available PDF files
python pdf_question_parser.py --list-pdfs

# Show help
python pdf_question_parser.py --help
```

#### Comparison Example
```bash
# Extract both questions and solutions from the same PDF
python pdf_question_parser.py document.pdf -o questions.json      # Questions
python pdf_question_parser.py document.pdf -s -o solutions.json   # Solutions
```

### Output Formats

#### Enhanced Question Output Format (JSON)
```json
{
  "question": "Question text here - preserve any image references like ![img-X.jpeg](img-X.jpeg)",
  "options": {
    "A": "a. option text",
    "B": "b. option text",
    "C": "c. option text",
    "D": "d. option text"
  },
  "answer": "A",
  "solution": {
    "steps": [
      "Step 1: Detailed explanation of the first step",
      "Step 2: Detailed explanation of the second step",
      "Step 3: Continue with all solution steps..."
    ],
    "methodology": "Brief description of the solution approach/method used",
    "key_concepts": ["concept1", "concept2", "concept3"],
    "final_explanation": "Final explanation connecting the solution to the answer"
  },
  "hints": [
    "Hint 1: First helpful hint for solving this question",
    "Hint 2: Second helpful hint or approach suggestion"
  ]
}
```

#### Solution Output Format (JSON)
```json
{
  "question_number": "1",
  "question_text": "Brief question text or reference",
  "solution_steps": [
    "Step 1: Detailed explanation of the first step",
    "Step 2: Detailed explanation of the second step",
    "Step 3: Continue with all solution steps..."
  ],
  "final_answer": "The final answer or conclusion",
  "methodology": "Brief description of the solution approach/method used",
  "key_concepts": ["concept1", "concept2", "concept3"]
}
```

## Project Structure

### Core Files
- `pdf_question_parser.py` - Main script with CLI interface for both questions and solutions
- `question_extractor.py` - Question and solution extraction logic using Mistral AI
- `output_formatter.py` - Output formatting and validation utilities
- `key.txt` - API key file (not included in repository)

### Documentation
- `README.md` - This file with basic usage information
- `INSTALLATION.md` - Detailed installation and setup guide
- `SOLUTION_EXTRACTION_GUIDE.md` - Comprehensive guide for solution extraction feature
- `MISTRAL_AI_SOLUTION_PROMPT.md` - Technical details of the Mistral AI prompts

### Configuration
- `requirements.txt` - Python package dependencies

## API Configuration

The script uses the Mistral AI API with the following configuration:
- OCR Model: `mistral-ocr-latest`
- Chat Model: `mistral-small-latest`
- API Endpoint: `https://api.mistral.ai/`

## Limitations

- PDF files must not exceed 50 MB in size
- Documents should be no longer than 1,000 pages
- Optimized for educational/test materials with multiple-choice questions

## Example

```bash
python pdf_question_parser.py "test_document.pdf" -o "extracted_questions.txt"
```

This will process `test_document.pdf` and save the formatted questions to `extracted_questions.txt`.

## Error Handling

The script includes comprehensive error handling for:
- Missing or invalid API keys
- Corrupted or unreadable PDF files
- Network connectivity issues
- Invalid document formats

## License

This project is provided as-is for educational and research purposes.
