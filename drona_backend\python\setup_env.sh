#!/bin/bash
# Setup script for Linux/Mac to configure environment variables for Gemini AI

echo "Setting up Gemini AI environment..."

# Set the Gemini API key
export GEMINI_API_KEY=AIzaSyB9NGae4_z5JCvk2wEVHBVrrM2a8RtgoMw

# Create the Mistral API key file
echo "PchpFHOtBOO5cM3j0JUUucULwNcaMIhj" > key.txt

echo "Environment setup complete!"
echo ""
echo "To make the environment variable permanent, add this to your ~/.bashrc or ~/.zshrc:"
echo "export GEMINI_API_KEY=AIzaSyB9NGae4_z5JCvk2wEVHBVrrM2a8RtgoMw"
echo ""
echo "To verify setup, run:"
echo "python test_gemini.py"
echo ""
echo "To start the API server, run:"
echo "python api_server.py"
