#!/usr/bin/env python3
"""
Test script for large PDF file processing
"""

import os
import time
import requests
import json

def test_large_file_processing(pdf_path, server_url="http://localhost:5000"):
    """
    Test large file processing with progress monitoring
    
    Args:
        pdf_path (str): Path to the PDF file to test
        server_url (str): URL of the API server
    """
    
    if not os.path.exists(pdf_path):
        print(f"❌ File not found: {pdf_path}")
        return False
    
    file_size = os.path.getsize(pdf_path)
    file_size_mb = file_size / (1024 * 1024)
    
    print(f"🚀 Testing large file processing...")
    print(f"📄 File: {os.path.basename(pdf_path)}")
    print(f"📊 Size: {file_size_mb:.1f}MB ({file_size:,} bytes)")
    
    if file_size_mb > 100:
        print(f"⚠️ Very large file detected. Processing may take 10-20 minutes...")
    elif file_size_mb > 50:
        print(f"⏳ Large file detected. Processing may take 5-10 minutes...")
    else:
        print(f"⚡ Normal file size. Expected processing time: 1-3 minutes...")
    
    print(f"🔗 Server: {server_url}")
    print("=" * 60)
    
    # Test server health first
    try:
        health_response = requests.get(f"{server_url}/", timeout=10)
        if health_response.status_code == 200:
            health_data = health_response.json()
            print(f"✅ Server is healthy")
            print(f"   Service: {health_data.get('service', 'Unknown')}")
            print(f"   AI Provider: {health_data.get('ai_provider', 'Unknown')}")
        else:
            print(f"❌ Server health check failed: {health_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Cannot connect to server: {e}")
        return False
    
    # Upload and process the file
    start_time = time.time()
    
    try:
        with open(pdf_path, 'rb') as f:
            files = {'file': (os.path.basename(pdf_path), f, 'application/pdf')}
            data = {'ai_provider': 'gemini'}  # Use Gemini AI
            
            print(f"\n🔄 Uploading and processing file...")
            print(f"⏳ Please wait... (this may take several minutes for large files)")
            
            # Use extended timeout for large files
            timeout = max(600, file_size_mb * 10)  # At least 10 minutes, or 10 seconds per MB
            print(f"⏰ Timeout set to: {timeout} seconds")
            
            response = requests.post(
                f"{server_url}/api/extract",
                files=files,
                data=data,
                timeout=timeout
            )
            
            processing_time = time.time() - start_time
            
            print(f"\n📊 Processing completed in {processing_time:.1f} seconds ({processing_time/60:.1f} minutes)")
            
            if response.status_code == 200:
                result = response.json()
                
                print(f"✅ Success!")
                print(f"   Status: {result.get('status', 'Unknown')}")
                print(f"   Questions found: {result.get('questions_count', 0)}")
                
                if 'performance_metrics' in result:
                    metrics = result['performance_metrics']
                    print(f"\n📈 Performance Metrics:")
                    print(f"   Total duration: {metrics.get('total_duration', 0):.1f}s")
                    print(f"   OCR duration: {metrics.get('ocr_duration', 0):.1f}s")
                    print(f"   AI duration: {metrics.get('ai_duration', 0):.1f}s")
                    print(f"   JSON processing: {metrics.get('json_duration', 0):.1f}s")
                    print(f"   OCR text chars: {metrics.get('ocr_text_chars', 0):,}")
                    print(f"   OCR images: {metrics.get('ocr_images', 0)}")
                
                # Show sample questions
                if 'data' in result and result['data']:
                    print(f"\n📝 Sample Questions:")
                    for i, question in enumerate(result['data'][:3]):  # Show first 3 questions
                        q_text = question.get('question', 'No question text')[:100]
                        print(f"   {i+1}. {q_text}{'...' if len(q_text) >= 100 else ''}")
                
                return True
                
            else:
                print(f"❌ Processing failed: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"   Error: {error_data.get('error', 'Unknown error')}")
                except:
                    print(f"   Response: {response.text[:200]}...")
                return False
                
    except requests.exceptions.Timeout:
        processing_time = time.time() - start_time
        print(f"⏰ Request timed out after {processing_time:.1f} seconds")
        print(f"   This may happen with very large files. Consider:")
        print(f"   - Splitting the PDF into smaller files")
        print(f"   - Compressing the PDF")
        print(f"   - Increasing server timeout settings")
        return False
        
    except Exception as e:
        processing_time = time.time() - start_time
        print(f"❌ Error after {processing_time:.1f} seconds: {e}")
        return False

def main():
    """Main function to run the test"""
    import sys
    
    if len(sys.argv) != 2:
        print("Usage: python large_file_test.py <path_to_pdf>")
        print("Example: python large_file_test.py large_document.pdf")
        sys.exit(1)
    
    pdf_path = sys.argv[1]
    success = test_large_file_processing(pdf_path)
    
    if success:
        print(f"\n🎉 Large file processing test completed successfully!")
    else:
        print(f"\n💥 Large file processing test failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
