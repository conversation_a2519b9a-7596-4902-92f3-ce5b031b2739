"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QuestionsService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const question_schema_1 = require("../schema/question.schema");
const mistral_ai_service_1 = require("../mistral-ai/mistral-ai.service");
const image_compression_service_1 = require("../common/services/image-compression.service");
const axios_1 = require("@nestjs/axios");
const rxjs_1 = require("rxjs");
const FormData = require("form-data");
let QuestionsService = class QuestionsService {
    constructor(questionModel, mistralAiService, imageCompressionService, httpService) {
        this.questionModel = questionModel;
        this.mistralAiService = mistralAiService;
        this.imageCompressionService = imageCompressionService;
        this.httpService = httpService;
    }
    async create(createQuestionDto, user, images) {
        if (!user || !user._id) {
            throw new common_1.BadRequestException('User information is required to create a question');
        }
        const duplicateQuestion = await this.checkForDuplicateQuestion(createQuestionDto.content);
        if (duplicateQuestion) {
            throw new common_1.BadRequestException(`A question with similar content already exists in the database (ID: ${duplicateQuestion._id}). Please modify the question content to make it unique.`);
        }
        let imageUrls = [];
        if (images && images.length > 0) {
            try {
                const compressionPromises = images.map((image) => this.imageCompressionService.compressAndSaveImage(image, {
                    maxSizeBytes: 2 * 1024 * 1024,
                    quality: 85,
                    format: 'jpeg',
                }));
                const compressedImages = await Promise.all(compressionPromises);
                imageUrls = compressedImages.map((result) => result.url);
                console.log(`Compressed ${images.length} images for question creation`);
            }
            catch (error) {
                throw new common_1.BadRequestException(`Image compression failed: ${error.message}`);
            }
        }
        const questionData = {
            ...createQuestionDto,
            imageUrls: [...(createQuestionDto.imageUrls || []), ...imageUrls],
            createdBy: user._id,
            reviewStatus: 'pending',
        };
        const createdQuestion = new this.questionModel(questionData);
        return createdQuestion.save();
    }
    async findAll(filters) {
        const query = {};
        if (filters.topicId && !filters.subjectId) {
            throw new common_1.BadRequestException('SubjectId is required when topicId is provided');
        }
        if (filters.subjectId) {
            query.subjectId = filters.subjectId;
        }
        if (filters.topicId) {
            query.topicId = filters.topicId;
        }
        if (filters.difficulty) {
            query.difficulty = filters.difficulty;
        }
        if (filters.type) {
            query.type = filters.type;
        }
        if (filters.reviewStatus) {
            query.reviewStatus = filters.reviewStatus;
        }
        if (filters.search) {
            query.$or = [
                { content: { $regex: filters.search, $options: 'i' } },
                { answer: { $regex: filters.search, $options: 'i' } },
            ];
        }
        const page = filters.page || 1;
        const limit = filters.limit || 20;
        const skip = (page - 1) * limit;
        const totalItems = await this.questionModel.countDocuments(query);
        const totalPages = Math.ceil(totalItems / limit);
        const questions = await this.questionModel
            .find(query)
            .select('_id content options answer difficulty type reviewStatus status createdAt solution hints')
            .populate('subjectId', '_id name')
            .populate('topicId', '_id name')
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(limit)
            .lean()
            .exec();
        return {
            questions,
            pagination: {
                currentPage: page,
                totalPages,
                totalItems,
                itemsPerPage: limit,
            },
        };
    }
    async findOne(id) {
        const question = await this.questionModel
            .findById(id)
            .populate('subjectId', '_id name')
            .populate('topicId', '_id name')
            .populate('createdBy', '_id displayName email')
            .populate('reviewedBy', '_id displayName email')
            .lean()
            .exec();
        if (!question) {
            throw new common_1.NotFoundException(`Question with ID ${id} not found`);
        }
        return question;
    }
    async update(id, updateQuestionDto, images) {
        if (updateQuestionDto.content) {
            const duplicateQuestion = await this.checkForDuplicateQuestionExcluding(updateQuestionDto.content, id);
            if (duplicateQuestion) {
                throw new common_1.BadRequestException(`A question with similar content already exists in the database (ID: ${duplicateQuestion._id}). Please modify the question content to make it unique.`);
            }
        }
        let newImageUrls = [];
        if (images && images.length > 0) {
            try {
                const compressionPromises = images.map((image) => this.imageCompressionService.compressAndSaveImage(image, {
                    maxSizeBytes: 2 * 1024 * 1024,
                    quality: 85,
                    format: 'jpeg',
                }));
                const compressedImages = await Promise.all(compressionPromises);
                newImageUrls = compressedImages.map((result) => result.url);
                console.log(`Compressed ${images.length} new images for question update`);
            }
            catch (error) {
                throw new common_1.BadRequestException(`Image compression failed: ${error.message}`);
            }
        }
        const updateData = { ...updateQuestionDto };
        if (newImageUrls.length > 0) {
            const existingQuestion = await this.questionModel.findById(id);
            if (existingQuestion) {
                updateData.imageUrls = [
                    ...(existingQuestion.imageUrls || []),
                    ...newImageUrls,
                ];
            }
            else {
                updateData.imageUrls = newImageUrls;
            }
        }
        const updatedQuestion = await this.questionModel
            .findByIdAndUpdate(id, updateData, { new: true })
            .populate('subjectId')
            .populate('topicId')
            .populate('createdBy')
            .exec();
        if (!updatedQuestion) {
            throw new common_1.NotFoundException(`Question with ID ${id} not found`);
        }
        return updatedQuestion;
    }
    async remove(id) {
        const deletedQuestion = await this.questionModel
            .findByIdAndDelete(id)
            .exec();
        if (!deletedQuestion) {
            throw new common_1.NotFoundException(`Question with ID ${id} not found`);
        }
        return deletedQuestion;
    }
    async findDuplicates(filters) {
        const limit = filters?.limit || 50;
        const pipeline = [
            ...(filters?.subjectId
                ? [{ $match: { subjectId: filters.subjectId } }]
                : []),
            {
                $group: {
                    _id: '$content',
                    questions: { $push: '$$ROOT' },
                    count: { $sum: 1 },
                },
            },
            {
                $match: {
                    count: { $gt: 1 },
                },
            },
            { $limit: limit },
            {
                $lookup: {
                    from: 'subjects',
                    localField: 'questions.subjectId',
                    foreignField: '_id',
                    as: 'subjectInfo',
                },
            },
            {
                $project: {
                    _id: 0,
                    content: '$_id',
                    duplicateCount: '$count',
                    questions: {
                        $map: {
                            input: '$questions',
                            as: 'question',
                            in: {
                                _id: '$$question._id',
                                content: '$$question.content',
                                difficulty: '$$question.difficulty',
                                type: '$$question.type',
                                reviewStatus: '$$question.reviewStatus',
                                createdAt: '$$question.createdAt',
                                subject: {
                                    $arrayElemAt: [
                                        {
                                            $filter: {
                                                input: '$subjectInfo',
                                                cond: { $eq: ['$$this._id', '$$question.subjectId'] },
                                            },
                                        },
                                        0,
                                    ],
                                },
                            },
                        },
                    },
                },
            },
        ];
        const duplicateGroups = await this.questionModel.aggregate(pipeline).exec();
        const result = duplicateGroups.map((group) => ({
            originalQuestion: group.questions[0],
            duplicates: group.questions.slice(1).map((q) => ({
                ...q,
                similarity: 'exact_match',
            })),
        }));
        return {
            duplicateGroups: result,
            totalGroups: result.length,
        };
    }
    async findPendingReviews(filters) {
        const query = {
            reviewStatus: 'pending',
        };
        if (filters?.subjectId) {
            query.subjectId = filters.subjectId;
        }
        const page = filters?.page || 1;
        const limit = filters?.limit || 20;
        const skip = (page - 1) * limit;
        const totalItems = await this.questionModel.countDocuments(query);
        const totalPages = Math.ceil(totalItems / limit);
        const questions = await this.questionModel
            .find(query)
            .select('_id content options answer difficulty type createdAt solution hints')
            .populate('subjectId', '_id name')
            .populate('topicId', '_id name')
            .sort({ createdAt: -1 })
            .skip(skip)
            .limit(limit)
            .lean()
            .exec();
        return {
            questions,
            pagination: {
                currentPage: page,
                totalPages,
                totalItems,
                itemsPerPage: limit,
            },
        };
    }
    async removeQuestion(id) {
        const question = await this.questionModel.findById(id).exec();
        if (!question) {
            throw new common_1.NotFoundException(`Question with ID ${id} not found`);
        }
        await this.questionModel.deleteOne({ _id: id }).exec();
    }
    async getDebugCounts() {
        const total = await this.questionModel.countDocuments();
        const byReviewStatus = {
            pending: await this.questionModel.countDocuments({ reviewStatus: 'pending' }),
            approved: await this.questionModel.countDocuments({ reviewStatus: 'approved' }),
            rejected: await this.questionModel.countDocuments({ reviewStatus: 'rejected' }),
        };
        const byStatus = {
            active: await this.questionModel.countDocuments({ status: 'active' }),
            inactive: await this.questionModel.countDocuments({ status: 'inactive' }),
        };
        const sampleQuestions = await this.questionModel
            .find({})
            .select('_id content reviewStatus status subjectId createdAt')
            .populate('subjectId', 'name')
            .limit(5)
            .lean()
            .exec();
        return {
            total,
            byReviewStatus,
            byStatus,
            sampleQuestions,
        };
    }
    escapeRegExp(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }
    normalizeQuestionContent(content) {
        if (!content || typeof content !== 'string') {
            return '';
        }
        return content
            .replace(/\s+/g, '')
            .replace(/[\n\r]/g, '')
            .replace(/[^\w\d]/g, '')
            .toLowerCase()
            .trim();
    }
    async checkForDuplicateQuestion(content) {
        const normalizedContent = this.normalizeQuestionContent(content);
        const allQuestions = await this.questionModel
            .find({}, { content: 1 })
            .lean()
            .exec();
        for (const question of allQuestions) {
            const existingNormalizedContent = this.normalizeQuestionContent(question.content);
            if (existingNormalizedContent === normalizedContent) {
                return question;
            }
        }
        return null;
    }
    async checkForDuplicateQuestionExcluding(content, excludeId) {
        const normalizedContent = this.normalizeQuestionContent(content);
        const allQuestions = await this.questionModel
            .find({ _id: { $ne: excludeId } }, { content: 1 })
            .lean()
            .exec();
        for (const question of allQuestions) {
            const existingNormalizedContent = this.normalizeQuestionContent(question.content);
            if (existingNormalizedContent === normalizedContent) {
                return question;
            }
        }
        return null;
    }
    async reviewQuestion(id, reviewDto, user) {
        if (!reviewDto || !reviewDto.status) {
            throw new common_1.BadRequestException('Review status is required');
        }
        if (!['approved', 'rejected'].includes(reviewDto.status)) {
            throw new common_1.BadRequestException('Status must be either approved or rejected');
        }
        const question = await this.questionModel.findById(id);
        if (!question) {
            throw new common_1.NotFoundException(`Question with ID ${id} not found`);
        }
        question.reviewStatus = reviewDto.status;
        question.reviewedBy = user._id;
        question.reviewDate = new Date();
        question.reviewNotes = reviewDto.notes || '';
        if (reviewDto.status === 'approved') {
            question.status = 'active';
        }
        return question.save();
    }
    async bulkReview(questionIds, reviewDto, user) {
        const questions = await this.questionModel.find({
            _id: { $in: questionIds },
        });
        if (questions.length === 0) {
            throw new common_1.NotFoundException('No questions found with the provided IDs');
        }
        if (questions.length !== questionIds.length) {
            const foundIds = questions.map((q) => q._id.toString());
            const missingIds = questionIds.filter((id) => !foundIds.includes(id));
            throw new common_1.NotFoundException(`Some questions were not found: ${missingIds.join(', ')}`);
        }
        const alreadyReviewed = questions.filter((q) => q.reviewStatus !== 'pending');
        if (alreadyReviewed.length > 0) {
            const reviewedIds = alreadyReviewed.map((q) => q._id.toString());
            throw new common_1.BadRequestException(`Some questions are already reviewed: ${reviewedIds.join(', ')}`);
        }
        const bulkOps = questions.map((question) => ({
            updateOne: {
                filter: { _id: question._id },
                update: {
                    $set: {
                        reviewStatus: reviewDto.status,
                        reviewedBy: user._id,
                        reviewDate: new Date(),
                        reviewNotes: reviewDto.notes || '',
                        ...(reviewDto.status === 'approved' ? { status: 'active' } : {}),
                    },
                },
            },
        }));
        const result = await this.questionModel.bulkWrite(bulkOps);
        return {
            message: `Successfully reviewed ${result.modifiedCount} questions`,
            reviewedCount: result.modifiedCount,
            status: reviewDto.status,
            details: {
                matchedCount: result.matchedCount,
                modifiedCount: result.modifiedCount,
                upsertedCount: result.upsertedCount,
                deletedCount: result.deletedCount,
            },
        };
    }
    async bulkUploadFromPdf(file, uploadDto, user) {
        try {
            const formData = new FormData();
            formData.append('file', file.buffer, {
                filename: file.originalname,
                contentType: file.mimetype,
            });
            formData.append('ai_provider', 'mistral');
            const response = await (0, rxjs_1.firstValueFrom)(this.httpService.post('http://localhost:5000/api/extract', formData, {
                headers: {
                    ...formData.getHeaders(),
                },
                timeout: 15 * 60 * 1000,
            }));
            const extractedData = response.data;
            if (!extractedData || !extractedData.data || !Array.isArray(extractedData.data)) {
                throw new common_1.BadRequestException('Invalid response format from extraction API');
            }
            if (extractedData.data.length === 0) {
                throw new common_1.BadRequestException('No questions found in the PDF');
            }
            const result = await this.saveExtractedQuestions(extractedData.data, uploadDto.subjectId, uploadDto.topicId, user._id);
            return {
                message: `PDF processed successfully. ${result.questionsAdded} questions added, ${result.questionsFailed} failed.`,
                questionsAdded: result.questionsAdded,
                questionsFailed: result.questionsFailed,
                questions: result.questions,
                errors: result.errors,
            };
        }
        catch (error) {
            if (error.code === 'ECONNREFUSED') {
                throw new common_1.BadRequestException('Unable to connect to extraction service. Please ensure the service is running on localhost:5000');
            }
            if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
                throw new common_1.BadRequestException('PDF processing timed out. Please try with a smaller file or try again later.');
            }
            throw new common_1.BadRequestException(`Failed to process PDF: ${error.message}`);
        }
    }
    async saveExtractedQuestions(extractedQuestions, subjectId, topicId, userId) {
        const result = {
            questionsAdded: 0,
            questionsFailed: 0,
            questions: [],
            errors: [],
        };
        if (!mongoose_2.Types.ObjectId.isValid(subjectId)) {
            throw new common_1.BadRequestException(`Invalid subjectId format: ${subjectId}`);
        }
        if (!mongoose_2.Types.ObjectId.isValid(topicId)) {
            throw new common_1.BadRequestException(`Invalid topicId format: ${topicId}`);
        }
        if (!mongoose_2.Types.ObjectId.isValid(userId)) {
            throw new common_1.BadRequestException(`Invalid userId format: ${userId}`);
        }
        for (const [index, questionData] of extractedQuestions.entries()) {
            try {
                if (!questionData.question || !questionData.options || !questionData.answer) {
                    throw new Error('Missing required fields: question, options, or answer');
                }
                let optionsArray = [];
                if (typeof questionData.options === 'object' && !Array.isArray(questionData.options)) {
                    optionsArray = Object.values(questionData.options);
                }
                else if (Array.isArray(questionData.options)) {
                    optionsArray = questionData.options;
                }
                else {
                    throw new Error('Invalid options format');
                }
                if (optionsArray.length < 2) {
                    throw new Error(`Insufficient options: ${optionsArray.length} (minimum 2 required)`);
                }
                let answerText = questionData.answer;
                if (typeof questionData.options === 'object' && !Array.isArray(questionData.options)) {
                    if (questionData.options[questionData.answer]) {
                        answerText = questionData.options[questionData.answer];
                    }
                }
                const questionDoc = {
                    content: questionData.question,
                    options: optionsArray,
                    answer: answerText,
                    subjectId: new mongoose_2.Types.ObjectId(subjectId),
                    topicId: new mongoose_2.Types.ObjectId(topicId),
                    difficulty: 'medium',
                    type: 'multiple-choice',
                    createdBy: new mongoose_2.Types.ObjectId(userId),
                    reviewStatus: 'pending',
                    status: 'inactive',
                    source: 'pdf-extract',
                    solution: questionData.solution || null,
                    hints: questionData.hints || [],
                    imageUrls: [],
                };
                const imageMatches = questionData.question.match(/!\[.*?\]\(.*?\)/g);
                if (imageMatches) {
                    questionDoc.imageUrls = imageMatches.map(match => {
                        const urlMatch = match.match(/\((.*?)\)/);
                        return urlMatch ? urlMatch[1] : '';
                    }).filter(url => url);
                }
                const createdQuestion = new this.questionModel(questionDoc);
                await createdQuestion.validate();
                const savedQuestion = await createdQuestion.save();
                result.questions.push(savedQuestion);
                result.questionsAdded++;
            }
            catch (error) {
                result.questionsFailed++;
                const errorMessage = `Failed to create question ${index + 1}: ${error.message}`;
                result.errors.push(errorMessage);
            }
        }
        return result;
    }
};
exports.QuestionsService = QuestionsService;
exports.QuestionsService = QuestionsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(question_schema_1.Question.name)),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mistral_ai_service_1.MistralAiService,
        image_compression_service_1.ImageCompressionService,
        axios_1.HttpService])
], QuestionsService);
//# sourceMappingURL=questions.service.js.map